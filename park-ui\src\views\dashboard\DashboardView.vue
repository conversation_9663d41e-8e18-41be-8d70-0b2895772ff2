<template>
  <div class="dashboard-container">
    <!-- KPI指标区域 -->
    <div class="kpi-section">
      <div class="kpi-grid">
        <KpiCard 
          v-for="kpi in kpiData" 
          :key="kpi.key"
          :title="kpi.title"
          :value="kpi.value"
          :trend="kpi.trend"
          :icon="kpi.icon"
        />
      </div>
    </div>

    <!-- 主要可视化区域 -->
    <div class="main-section">
      <!-- 地图容器 -->
      <div class="map-container">
        <div id="amap-container"></div>
        <div class="map-controls">
          <el-button-group>
            <el-button 
              :type="mapType === 'parking' ? 'primary' : 'default'"
              @click="switchMapType('parking')"
            >停车场库</el-button>
            <el-button 
              :type="mapType === 'charging' ? 'primary' : 'default'"
              @click="switchMapType('charging')"
            >充电场库</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="chart-item">
          <h3>订单趋势</h3>
          <div id="order-chart" style="height: 200px;"></div>
        </div>
        <div class="chart-item">
          <h3>收入分析</h3>
          <div id="revenue-chart" style="height: 200px;"></div>
        </div>
      </div>
    </div>

    <!-- 底部数据区域 -->
    <div class="bottom-section">
      <div class="data-panel">
        <h3>实时数据流</h3>
        <div class="stream-list">
          <div v-for="item in realtimeData" :key="item.id" class="stream-item">
            <span class="time">{{ item.time }}</span>
            <span class="event">{{ item.event }}</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>

      <div class="data-panel">
        <h3>场库排行</h3>
        <div class="ranking-list">
          <div v-for="(item, index) in rankingData" :key="item.id" class="rank-item">
            <span class="rank">{{ index + 1 }}</span>
            <span class="name">{{ item.name }}</span>
            <span class="orders">{{ item.orders }}单</span>
          </div>
        </div>
      </div>

      <div class="data-panel">
        <h3>系统告警</h3>
        <div class="alert-list">
          <el-alert
            v-for="alert in alertData"
            :key="alert.id"
            :title="alert.title"
            :type="alert.type"
            size="small"
            :closable="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import AMapLoader from '@amap/amap-jsapi-loader'
import * as echarts from 'echarts'
import KpiCard from './components/KpiCard.vue'

// 响应式数据
const kpiData = ref([
  { key: 'warehouses', title: '总场库数量', value: 28, trend: '+5%', icon: 'OfficeBuilding' },
  { key: 'online', title: '在线场库', value: 25, trend: '+2%', icon: 'Connection' },
  { key: 'orders', title: '今日订单', value: 156, trend: '+12%', icon: 'Document' },
  { key: 'revenue', title: '实时收入', value: '¥12,580', trend: '+8%', icon: 'Money' },
  { key: 'users', title: '活跃用户', value: 89, trend: '+15%', icon: 'User' }
])

const mapType = ref('parking')
const realtimeData = ref([
  { id: 1, time: '14:32', event: '新订单', value: '¥15' },
  { id: 2, time: '14:30', event: '用户注册', value: '1人' },
  { id: 3, time: '14:28', event: '支付完成', value: '¥25' }
])
const rankingData = ref([
  { id: 1, name: '万达广场停车场', orders: 156 },
  { id: 2, name: '银泰城停车场', orders: 142 },
  { id: 3, name: '恒隆广场停车场', orders: 128 }
])
const alertData = ref([
  { id: 1, title: '系统运行正常', type: 'success' },
  { id: 2, title: '2个场库离线', type: 'warning' }
])

// 地图和图表实例
let mapInstance = null
let orderChart = null
let revenueChart = null

// 初始化地图
const initMap = async () => {
  try {
    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: import.meta.env.VITE_AMAP_JS_SECURITY,
    }

    const AMap = await AMapLoader.load({
      key: import.meta.env.VITE_AMAP_JS_KEY,
      version: '2.0',
      plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.HeatMap', 'AMap.MarkerCluster']
    })

    mapInstance = new AMap.Map('amap-container', {
      zoom: 11,
      center: [121.81, 30.88],
      mapStyle: 'amap://styles/blue',
      showLabel: true
    })

    // 添加控件
    mapInstance.addControl(new AMap.Scale())
    mapInstance.addControl(new AMap.ToolBar())

    // 加载场库数据
    await loadWarehouseData()

  } catch (error) {
    console.error('地图初始化失败:', error)
    ElMessage.error('地图加载失败')
  }
}

// 加载场库数据
const loadWarehouseData = async () => {
  try {
    // 模拟场库数据
    const warehouses = [
      { id: 1, name: '万达广场停车场', longitude: 121.82, latitude: 30.89 },
      { id: 2, name: '银泰城停车场', longitude: 121.80, latitude: 30.87 },
      { id: 3, name: '恒隆广场停车场', longitude: 121.83, latitude: 30.88 }
    ]
    
    const markers = warehouses.map(warehouse => {
      return new AMap.Marker({
        position: [warehouse.longitude, warehouse.latitude],
        title: warehouse.name,
        icon: new AMap.Icon({
          size: new AMap.Size(32, 32),
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiM0MDlFRkYiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTIgNEgxNFYxMkgyVjRaIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+Cjwvc3ZnPgo='
        })
      })
    })
    
    mapInstance.add(markers)
    
  } catch (error) {
    console.error('加载场库数据失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  // 订单趋势图
  orderChart = echarts.init(document.getElementById('order-chart'))
  orderChart.setOption({
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: { type: 'value' },
    series: [{
      data: [120, 132, 101, 134, 90, 230, 210],
      type: 'line',
      smooth: true,
      areaStyle: { color: 'rgba(64, 158, 255, 0.2)' }
    }]
  })

  // 收入分析图
  revenueChart = echarts.init(document.getElementById('revenue-chart'))
  revenueChart.setOption({
    tooltip: { trigger: 'item' },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: 1048, name: '停车费' },
        { value: 735, name: '充电费' },
        { value: 580, name: '套餐费' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  })
}

// 切换地图类型
const switchMapType = (type) => {
  mapType.value = type
  loadWarehouseData()
}

// 生命周期
onMounted(() => {
  initMap()
  initCharts()
})

onUnmounted(() => {
  if (mapInstance) mapInstance.destroy()
  if (orderChart) orderChart.dispose()
  if (revenueChart) revenueChart.dispose()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.kpi-section {
  margin-bottom: 20px;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
}

.main-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  height: 400px;
}

.map-container {
  flex: 2;
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#amap-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-item h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.bottom-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  height: 300px;
}

.data-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-panel h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  font-weight: 600;
}

.stream-item, .rank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
}

.stream-item:last-child, .rank-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.time {
  color: #666;
  font-size: 12px;
}

.event {
  color: #333;
}

.value {
  color: #409eff;
  font-weight: 600;
}

.name {
  flex: 1;
  margin: 0 10px;
  color: #333;
}

.orders {
  color: #409eff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .kpi-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .main-section {
    flex-direction: column;
    height: auto;
  }

  .map-container {
    height: 400px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    height: auto;
  }

  .data-panel {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-container {
    padding: 10px;
  }
}
</style>
