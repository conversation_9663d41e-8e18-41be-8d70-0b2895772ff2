<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临港捷运停车管理系统 - 星空版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow: hidden;
        }

        /* 星空背景 */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
            overflow: hidden;
            z-index: -1;
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, #ddd, transparent),
                radial-gradient(1px 1px at 50px 50px, #fff, transparent),
                radial-gradient(1px 1px at 80px 10px, #fff, transparent),
                radial-gradient(2px 2px at 130px 80px, #eee, transparent),
                radial-gradient(1px 1px at 70px 40px, #fff, transparent);
            background-size: 200px 200px;
            animation: drift 60s linear infinite;
        }

        .stars::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(1px 1px at 30px 60px, #fff, transparent),
                radial-gradient(2px 2px at 90px 40px, #eee, transparent),
                radial-gradient(1px 1px at 130px 80px, #fff, transparent);
            background-size: 250px 250px;
            animation: drift 120s linear infinite;
        }

        @keyframes drift {
            from { transform: translateX(0); }
            to { transform: translateX(200px); }
        }

        /* 流星效果 */
        .meteor {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
            animation: meteor 3s linear infinite;
            opacity: 0;
        }

        @keyframes meteor {
            0% {
                opacity: 0;
                transform: translateX(0) translateY(0);
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateX(300px) translateY(300px);
            }
        }

        /* 顶部导航 */
        .header {
            background: rgba(10, 10, 20, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .system-tag {
            background: rgba(102, 126, 234, 0.2);
            border: 1px solid rgba(102, 126, 234, 0.5);
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 14px;
            color: #a8b8ff;
            backdrop-filter: blur(10px);
        }

        .time-display {
            font-family: 'Monaco', monospace;
            color: #64ffda;
            font-size: 14px;
            text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.05);
            padding: 8px 16px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        /* 主容器 */
        .main-container {
            display: flex;
            height: calc(100vh - 70px);
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: rgba(15, 15, 25, 0.6);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 30px 0;
        }

        .menu-item {
            padding: 15px 30px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
            transition: width 0.3s;
        }

        .menu-item:hover {
            color: #64ffda;
            transform: translateX(5px);
        }

        .menu-item:hover::before {
            width: 100%;
        }

        .menu-item.active {
            color: #64ffda;
            background: rgba(100, 255, 218, 0.1);
            border-left: 3px solid #64ffda;
            box-shadow: inset 0 0 20px rgba(100, 255, 218, 0.1);
        }

        /* 内容区域 */
        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            position: relative;
        }

        /* 统计卡片 */
        .stat-cards {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin-bottom: 20px;
            box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(102, 126, 234, 0.5); }
            50% { transform: scale(1.05); box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }

        .stat-value {
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(135deg, #fff 0%, #a8b8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* 今日单量特殊样式 */
        .today-stat {
            background: rgba(100, 255, 218, 0.05);
            border-color: rgba(100, 255, 218, 0.3);
        }

        .today-stat .stat-icon {
            background: linear-gradient(135deg, #64ffda 0%, #48d1cc 100%);
            box-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
        }

        /* 主内容区 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 450px;
            gap: 30px;
            height: calc(100vh - 280px);
        }

        /* 地图容器 */
        .map-container {
            background: rgba(15, 15, 25, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        .map-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(100, 255, 218, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* 地图网格 */
        .map-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(102, 126, 234, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(102, 126, 234, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* 地图标记 */
        .map-marker {
            position: absolute;
            width: 50px;
            height: 50px;
            cursor: pointer;
        }

        .marker-inner {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
            animation: float 3s ease-in-out infinite;
        }

        .marker-inner span {
            transform: rotate(45deg);
            font-weight: bold;
            font-size: 18px;
        }

        .marker-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 2px solid rgba(102, 126, 234, 0.5);
            animation: pulse-ring 2s linear infinite;
        }

        @keyframes pulse-ring {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
        }

        @keyframes float {
            0%, 100% { transform: rotate(-45deg) translateY(0); }
            50% { transform: rotate(-45deg) translateY(-10px); }
        }

        /* 右侧面板 */
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        /* 图表容器 */
        .chart-container {
            background: rgba(15, 15, 25, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            height: 350px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #fff 0%, #a8b8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 圆环图 */
        .donut-chart {
            width: 220px;
            height: 220px;
            margin: 20px auto;
            position: relative;
        }

        .donut-svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
            filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.5));
        }

        .donut-bg {
            fill: none;
            stroke: rgba(255, 255, 255, 0.1);
            stroke-width: 30;
        }

        .donut-segment1 {
            fill: none;
            stroke: url(#gradient1);
            stroke-width: 30;
            stroke-dasharray: 440;
            stroke-dashoffset: 110;
            animation: draw 1.5s ease-out forwards;
        }

        .donut-segment2 {
            fill: none;
            stroke: url(#gradient2);
            stroke-width: 30;
            stroke-dasharray: 440;
            stroke-dashoffset: 330;
            animation: draw 1.5s ease-out 0.5s forwards;
        }

        @keyframes draw {
            to { stroke-dashoffset: 0; }
        }

        .donut-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .donut-total {
            font-size: 42px;
            font-weight: 700;
            background: linear-gradient(135deg, #64ffda 0%, #48d1cc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
        }

        .donut-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* 图例 */
        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            box-shadow: 0 0 10px currentColor;
        }

        /* 排行榜 */
        .ranking-container {
            background: rgba(15, 15, 25, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            flex: 1;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s;
            position: relative;
        }

        .ranking-item:hover {
            transform: translateX(10px);
            background: rgba(102, 126, 234, 0.05);
            padding-left: 15px;
            margin: 0 -15px;
        }

        .ranking-number {
            width: 32px;
            height: 32px;
            background: rgba(102, 126, 234, 0.2);
            border: 1px solid rgba(102, 126, 234, 0.5);
            color: #a8b8ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .ranking-number.top {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border: none;
            color: #000;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .ranking-name {
            flex: 1;
            color: rgba(255, 255, 255, 0.9);
        }

        .ranking-bar {
            flex: 2;
            height: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin: 0 15px;
            overflow: hidden;
            position: relative;
        }

        .ranking-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            position: relative;
            animation: fillBar 2s ease-out;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        .ranking-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: shimmer 2s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .ranking-amount {
            font-weight: 600;
            color: #64ffda;
            min-width: 100px;
            text-align: right;
            text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.8);
        }
    </style>
</head>
<body>
    <!-- 星空背景 -->
    <div class="starfield">
        <div class="stars"></div>
        <!-- 流星 -->
        <div class="meteor" style="top: 10%; left: 20%; animation-delay: 0s;"></div>
        <div class="meteor" style="top: 30%; left: 60%; animation-delay: 1s;"></div>
        <div class="meteor" style="top: 50%; left: 80%; animation-delay: 2s;"></div>
    </div>

    <!-- 顶部导航 -->
    <header class="header">
        <div class="logo">
            <span>🌌</span>
            <span>临港捷运</span>
        </div>
        <div class="header-right">
            <div class="system-tag">临港捷运停车管理系统</div>
            <div class="time-display">2025-08-02 09:42:25</div>
            <div class="user-info">
                <div class="avatar">👤</div>
                <span>jieyun04</span>
            </div>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="menu-item active">
                <span>🏠</span>
                <span>首页</span>
            </div>
            <div class="menu-item">
                <span>⚙️</span>
                <span>平台管理</span>
            </div>
            <div class="menu-item">
                <span>👥</span>
                <span>商户管理</span>
            </div>
            <div class="menu-item">
                <span>🚗</span>
                <span>车主管理</span>
            </div>
            <div class="menu-item">
                <span>💼</span>
                <span>财务报表</span>
            </div>
            <div class="menu-item">
                <span>📊</span>
                <span>运营管理</span>
            </div>
            <div class="menu-item">
                <span>🔧</span>
                <span>系统管理</span>
            </div>
            <div class="menu-item">
                <span>📝</span>
                <span>订单管理</span>
            </div>
        </aside>

        <!-- 内容区域 -->
        <main class="content">
            <!-- 统计卡片 -->
            <div class="stat-cards">
                <div class="stat-card">
                    <div class="stat-icon">🏢</div>
                    <div class="stat-value">25</div>
                    <div class="stat-label">场库总数量(个)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🚗</div>
                    <div class="stat-value">22,306</div>
                    <div class="stat-label">总车位数量(个)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-value">15,842</div>
                    <div class="stat-label">小程序用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-value">89,756</div>
                    <div class="stat-label">订单总数</div>
                </div>
                <div class="stat-card today-stat">
                    <div class="stat-icon">✨</div>
                    <div class="stat-value">257</div>
                    <div class="stat-label">今日单量</div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 地图容器 -->
                <div class="map-container">
                    <div class="map-grid"></div>
                    <!-- 地图标记 -->
                    <div class="map-marker" style="top: 20%; left: 25%;">
                        <div class="marker-pulse"></div>
                        <div class="marker-inner"><span>P1</span></div>
                    </div>
                    <div class="map-marker" style="top: 40%; left: 45%;">
                        <div class="marker-pulse"></div>
                        <div class="marker-inner"><span>P2</span></div>
                    </div>
                    <div class="map-marker" style="top: 60%; left: 65%;">
                        <div class="marker-pulse"></div>
                        <div class="marker-inner"><span>P3</span></div>
                    </div>
                    <div class="map-marker" style="top: 30%; left: 70%;">
                        <div class="marker-pulse"></div>
                        <div class="marker-inner"><span>P4</span></div>
                    </div>
                    <div class="map-marker" style="top: 75%; left: 35%;">
                        <div class="marker-pulse"></div>
                        <div class="marker-inner"><span>P5</span></div>
                    </div>
                </div>

                <!-- 右侧面板 -->
                <div class="right-panel">
                    <!-- 圆环图 -->
                    <div class="chart-container">
                        <h3 class="chart-title">🚗 今日进出场统计</h3>
                        <div class="donut-chart">
                            <svg class="donut-svg" viewBox="0 0 200 200">
                                <defs>
                                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                                    </linearGradient>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#64ffda;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#48d1cc;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <circle class="donut-bg" cx="100" cy="100" r="70"></circle>
                                <circle class="donut-segment1" cx="100" cy="100" r="70"></circle>
                                <circle class="donut-segment2" cx="100" cy="100" r="70"></circle>
                            </svg>
                            <div class="donut-center">
                                <div class="donut-total">257</div>
                                <div class="donut-label">总车次</div>
                            </div>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-dot" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                                <span>进场车次 (180)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-dot" style="background: linear-gradient(135deg, #64ffda 0%, #48d1cc 100%);"></div>
                                <span>出场车次 (77)</span>
                            </div>
                        </div>
                    </div>

                    <!-- 排行榜 -->
                    <div class="ranking-container">
                        <h3 class="chart-title">💰 今日场库金额排行</h3>
                        <div class="ranking-item">
                            <div class="ranking-number top">1</div>
                            <div class="ranking-name">新元鹤想苑</div>
                            <div class="ranking-bar">
                                <div class="ranking-fill" style="width: 100%;"></div>
                            </div>
                            <div class="ranking-amount">¥14,256</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number top">2</div>
                            <div class="ranking-name">新元理想苑</div>
                            <div class="ranking-bar">
                                <div class="ranking-fill" style="width: 85%;"></div>
                            </div>
                            <div class="ranking-amount">¥12,180</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number top">3</div>
                            <div class="ranking-name">方竹苑</div>
                            <div class="ranking-bar">
                                <div class="ranking-fill" style="width: 70%;"></div>
                            </div>
                            <div class="ranking-amount">¥10,052</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">4</div>
                            <div class="ranking-name">新元感想苑</div>
                            <div class="ranking-bar">
                                <div class="ranking-fill" style="width: 55%;"></div>
                            </div>
                            <div class="ranking-amount">¥7,896</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">5</div>
                            <div class="ranking-name">汇丰名都</div>
                            <div class="ranking-bar">
                                <div class="ranking-fill" style="width: 40%;"></div>
                            </div>
                            <div class="ranking-amount">¥5,732</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>