2025-08-02 00:17:49.008  INFO 28168 --- [main] com.lgjy.system.LgjySystemApplication    : The following 1 profile is active: "dev"
2025-08-02 00:17:49.904  INFO 28168 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 00:17:49.906  INFO 28168 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 00:17:49.935  INFO 28168 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-02 00:17:50.141  INFO 28168 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=20a971a3-cb48-330f-ad21-a538a4b7d629
2025-08-02 00:17:50.651  INFO 28168 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 9201 (http)
2025-08-02 00:17:50.662  INFO 28168 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-02 00:17:50.662  INFO 28168 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.105]
2025-08-02 00:17:50.678  WARN 28168 --- [main] o.a.c.webresources.DirResourceSet        : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.9201.3767829389610162493] which is part of the web application []
2025-08-02 00:17:50.804  INFO 28168 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-02 00:17:50.805  INFO 28168 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1771 ms
2025-08-02 00:17:50.896  INFO 28168 --- [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-08-02 00:17:51.475  INFO 28168 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-02 00:17:52.574  INFO 28168 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-gate' URL not provided. Will try picking an instance via load-balancing.
2025-08-02 00:17:52.666  INFO 28168 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-08-02 00:17:52.741  INFO 28168 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-wx' URL not provided. Will try picking an instance via load-balancing.
2025-08-02 00:17:54.194  INFO 28168 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-file' URL not provided. Will try picking an instance via load-balancing.
2025-08-02 00:17:54.415  INFO 28168 --- [main] c.a.c.sentinel.SentinelWebMvcConfigurer  : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-02 00:17:56.812  INFO 28168 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'park-system' URL not provided. Will try picking an instance via load-balancing.
2025-08-02 00:17:58.291  WARN 28168 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-02 00:17:58.300  INFO 28168 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-02 00:17:58.406  INFO 28168 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 9201 (http) with context path ''
2025-08-02 00:17:58.416  INFO 28168 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-02 00:17:58.417  INFO 28168 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-02 00:17:58.599  INFO 28168 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-system **************:9201 register finished
2025-08-02 00:17:59.806  INFO 28168 --- [main] com.lgjy.system.LgjySystemApplication    : Started LgjySystemApplication in 17.393 seconds (JVM running for 18.97)
2025-08-02 00:17:59.822  INFO 28168 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system.yml, group=DEFAULT_GROUP
2025-08-02 00:17:59.822  INFO 28168 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system, group=DEFAULT_GROUP
2025-08-02 00:17:59.824  INFO 28168 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-02 00:17:59.825  INFO 28168 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-system-dev.yml, group=DEFAULT_GROUP
2025-08-02 00:18:00.924  INFO 28168 --- [RMI TCP Connection(2)-**************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 00:18:00.926  INFO 28168 --- [RMI TCP Connection(2)-**************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-02 00:18:00.930  INFO 28168 --- [RMI TCP Connection(2)-**************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
