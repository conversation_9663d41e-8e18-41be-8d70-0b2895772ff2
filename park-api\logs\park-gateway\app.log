2025-08-02 00:17:00.468  INFO 30420 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : The following 1 profile is active: "dev"
2025-08-02 00:17:01.442  INFO 30420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 00:17:01.444  INFO 30420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 00:17:01.472  INFO 30420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-08-02 00:17:01.670  INFO 30420 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a28cdf3d-2497-3ef5-9b94-3ee1e1ff5151
2025-08-02 00:17:01.889  INFO 30420 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 00:17:01.891  INFO 30420 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 00:17:01.893  INFO 30420 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-02 00:17:08.005  INFO 30420 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-02 00:17:08.064  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-08-02 00:17:08.064  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-08-02 00:17:08.064  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-08-02 00:17:08.065  INFO 30420 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-02 00:17:08.349  INFO 30420 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-02 00:17:08.401  INFO 30420 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-02 00:17:08.633  WARN 30420 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-02 00:17:08.820  INFO 30420 --- [main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-08-02 00:17:11.159  INFO 30420 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-02 00:17:11.159  INFO 30420 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-02 00:17:11.361  INFO 30420 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-gateway 192.168.31.111:8080 register finished
2025-08-02 00:17:11.481  INFO 30420 --- [main] a.c.n.d.NacosDiscoveryHeartBeatPublisher : Start nacos heartBeat task scheduler.
2025-08-02 00:17:11.509  INFO 30420 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : Started LgjyGatewayApplication in 17.937 seconds (JVM running for 20.135)
2025-08-02 00:17:11.518  INFO 30420 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway, group=DEFAULT_GROUP
2025-08-02 00:17:11.520  INFO 30420 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway-dev.yml, group=DEFAULT_GROUP
2025-08-02 00:17:11.521  INFO 30420 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway.yml, group=DEFAULT_GROUP
2025-08-02 00:17:11.522  INFO 30420 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
