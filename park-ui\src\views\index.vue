<template>
  <div class="app-container">
    <div class="welcome-section">
      <h1>欢迎使用捷运停车管理系统</h1>
      <p>智慧停车管理解决方案</p>
    </div>
  </div>
</template>

<script setup>
// 首页基本设置
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-content {
  opacity: 0;
  animation: fadeIn 0.8s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.kpi-section {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
}

.main-section {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  height: 450px;
  position: relative;
  z-index: 1;
}

.map-container {
  flex: 2;
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#amap-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.chart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-item h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bottom-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  height: 350px;
  position: relative;
  z-index: 1;
}

.data-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.data-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.data-panel h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #2c3e50;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 实时数据流样式 */
.stream-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 14px;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.stream-item:hover {
  background: rgba(64, 158, 255, 0.05);
  padding-left: 8px;
  padding-right: 8px;
}

.stream-item:last-child {
  border-bottom: none;
}

.stream-icon {
  margin-right: 12px;
}

.icon-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.dot-order { background: #409eff; }
.dot-user { background: #67c23a; }
.dot-payment { background: #e6a23c; }
.dot-entry { background: #f56c6c; }
.dot-exit { background: #909399; }
.dot-charging { background: #9c27b0; }

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.stream-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time {
  color: #8492a6;
  font-size: 11px;
  font-weight: 500;
}

.event {
  color: #2c3e50;
  font-weight: 500;
}

.value {
  color: #409eff;
  font-weight: 600;
  font-size: 13px;
}

/* 排行榜样式 */
.rank-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.rank-item:hover {
  background: rgba(64, 158, 255, 0.05);
  padding-left: 8px;
  padding-right: 8px;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-right: 12px;
  color: white;
}

.rank-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; }
.rank-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%); color: #333; }
.rank-3 { background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%); }
.rank-4, .rank-5 { background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%); }

.rank-info {
  flex: 1;
}

.name {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 14px;
}

.stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.orders {
  color: #409eff;
  font-weight: 500;
}

.revenue {
  color: #67c23a;
  font-weight: 500;
}

.trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 50px;
  text-align: center;
}

.trend-up {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.trend-down {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .kpi-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .main-section {
    flex-direction: column;
    height: auto;
  }

  .map-container {
    height: 400px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    height: auto;
  }

  .data-panel {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-container {
    padding: 10px;
  }
}
</style>
