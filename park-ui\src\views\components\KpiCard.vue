<template>
  <el-card class="kpi-card" shadow="hover">
    <div class="kpi-content">
      <div class="kpi-icon" :style="{
        background: iconConfig.background,
        boxShadow: `0 4px 12px ${iconConfig.shadow}`
      }">
        <el-icon :size="28" :color="iconConfig.color">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="kpi-info">
        <div class="kpi-title">{{ title }}</div>
        <div class="kpi-value">{{ formattedValue }}</div>
        <div class="kpi-trend" :class="trendClass">
          <el-icon size="12">
            <ArrowUp v-if="trend.startsWith('+')" />
            <ArrowDown v-else />
          </el-icon>
          <span>{{ trend }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  ArrowUp, 
  ArrowDown, 
  OfficeBuilding, 
  Connection, 
  Document, 
  Money, 
  User 
} from '@element-plus/icons-vue'

const props = defineProps({
  title: String,
  value: [String, Number],
  trend: String,
  icon: String
})

const iconComponents = {
  OfficeBuilding,
  Connection,
  Document,
  Money,
  User
}

const iconComponent = computed(() => iconComponents[props.icon] || OfficeBuilding)

const iconConfig = computed(() => {
  const configs = {
    OfficeBuilding: {
      color: '#ffffff',
      background: 'linear-gradient(135deg, #409eff 0%, #66b3ff 100%)',
      shadow: 'rgba(64, 158, 255, 0.3)'
    },
    Connection: {
      color: '#ffffff',
      background: 'linear-gradient(135deg, #67c23a 0%, #85d65a 100%)',
      shadow: 'rgba(103, 194, 58, 0.3)'
    },
    Document: {
      color: '#ffffff',
      background: 'linear-gradient(135deg, #e6a23c 0%, #f0b659 100%)',
      shadow: 'rgba(230, 162, 60, 0.3)'
    },
    Money: {
      color: '#ffffff',
      background: 'linear-gradient(135deg, #f56c6c 0%, #ff8a8a 100%)',
      shadow: 'rgba(245, 108, 108, 0.3)'
    },
    User: {
      color: '#ffffff',
      background: 'linear-gradient(135deg, #909399 0%, #a6a9ad 100%)',
      shadow: 'rgba(144, 147, 153, 0.3)'
    }
  }
  return configs[props.icon] || configs.OfficeBuilding
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})

const trendClass = computed(() => {
  return props.trend.startsWith('+') ? 'trend-up' : 'trend-down'
})
</script>

<style scoped>
.kpi-card {
  height: 120px;
  transition: all 0.3s ease;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c, #f56c6c);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.kpi-card:hover::before {
  opacity: 1;
}

.kpi-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.kpi-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.kpi-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  pointer-events: none;
}

.kpi-info {
  flex: 1;
}

.kpi-title {
  font-size: 13px;
  color: #8492a6;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.kpi-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 6px;
  line-height: 1.1;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kpi-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  gap: 4px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  width: fit-content;
  backdrop-filter: blur(10px);
}

.trend-up {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.trend-down {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.2);
}

.trend-up .el-icon,
.trend-down .el-icon {
  font-weight: bold;
  font-size: 14px;
}
</style>
