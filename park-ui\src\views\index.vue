<template>
  <div class="dashboard-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载数据大屏...</div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-show="!loading" class="dashboard-content">
    <!-- KPI指标区域 -->
    <div class="kpi-section">
      <div class="kpi-grid">
        <KpiCard 
          v-for="kpi in kpiData" 
          :key="kpi.key"
          :title="kpi.title"
          :value="kpi.value"
          :trend="kpi.trend"
          :icon="kpi.icon"
        />
      </div>
    </div>

    <!-- 主要可视化区域 -->
    <div class="main-section">
      <!-- 地图容器 -->
      <div class="map-container">
        <div id="amap-container"></div>
        <div class="map-controls">
          <el-button-group>
            <el-button 
              :type="mapType === 'parking' ? 'primary' : 'default'"
              @click="switchMapType('parking')"
            >停车场库</el-button>
            <el-button 
              :type="mapType === 'charging' ? 'primary' : 'default'"
              @click="switchMapType('charging')"
            >充电场库</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="chart-item">
          <h3>订单趋势</h3>
          <div id="order-chart" style="height: 200px;"></div>
        </div>
        <div class="chart-item">
          <h3>收入分析</h3>
          <div id="revenue-chart" style="height: 200px;"></div>
        </div>
      </div>
    </div>

    <!-- 底部数据区域 -->
    <div class="bottom-section">
      <div class="data-panel">
        <h3>实时数据流</h3>
        <div class="stream-list">
          <div v-for="item in realtimeData" :key="item.id" class="stream-item" :class="`stream-${item.type}`">
            <div class="stream-icon">
              <div class="icon-dot" :class="`dot-${item.type}`"></div>
            </div>
            <div class="stream-content">
              <span class="time">{{ item.time }}</span>
              <span class="event">{{ item.event }}</span>
            </div>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>

      <div class="data-panel">
        <h3>场库排行</h3>
        <div class="ranking-list">
          <div v-for="(item, index) in rankingData" :key="item.id" class="rank-item">
            <div class="rank" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
            <div class="rank-info">
              <div class="name">{{ item.name }}</div>
              <div class="stats">
                <span class="orders">{{ item.orders }}单</span>
                <span class="revenue">¥{{ item.revenue }}</span>
              </div>
            </div>
            <div class="trend" :class="item.trend.startsWith('+') ? 'trend-up' : 'trend-down'">
              {{ item.trend }}
            </div>
          </div>
        </div>
      </div>

      <div class="data-panel">
        <h3>系统告警</h3>
        <div class="alert-list">
          <el-alert
            v-for="alert in alertData"
            :key="alert.id"
            :title="alert.title"
            :type="alert.type"
            size="small"
            :closable="false"
          />
        </div>
      </div>
    </div>

    </div> <!-- 结束 dashboard-content -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import AMapLoader from '@amap/amap-jsapi-loader'
import * as echarts from 'echarts'
import KpiCard from './components/KpiCard.vue'

// 响应式数据
const kpiData = ref([
  { key: 'warehouses', title: '总场库数量', value: 28, trend: '+5%', icon: 'OfficeBuilding' },
  { key: 'online', title: '在线场库', value: 25, trend: '+2%', icon: 'Connection' },
  { key: 'orders', title: '今日订单', value: 156, trend: '+12%', icon: 'Document' },
  { key: 'revenue', title: '实时收入', value: '¥12,580', trend: '+8%', icon: 'Money' },
  { key: 'users', title: '活跃用户', value: 89, trend: '+15%', icon: 'User' }
])

const mapType = ref('parking')
const loading = ref(true)
const mapLoaded = ref(false)
const chartsLoaded = ref(false)
const realtimeData = ref([
  { id: 1, time: '14:32', event: '新订单', value: '¥15', type: 'order' },
  { id: 2, time: '14:30', event: '用户注册', value: '1人', type: 'user' },
  { id: 3, time: '14:28', event: '支付完成', value: '¥25', type: 'payment' },
  { id: 4, time: '14:26', event: '车辆入场', value: '万达广场', type: 'entry' },
  { id: 5, time: '14:24', event: '车辆出场', value: '银泰城', type: 'exit' },
  { id: 6, time: '14:22', event: '充电完成', value: '¥8', type: 'charging' }
])

const rankingData = ref([
  { id: 1, name: '万达广场停车场', orders: 156, revenue: 2340, trend: '+12%' },
  { id: 2, name: '银泰城停车场', orders: 142, revenue: 2130, trend: '+8%' },
  { id: 3, name: '恒隆广场停车场', orders: 128, revenue: 1920, trend: '+5%' },
  { id: 4, name: '世纪金源停车场', orders: 98, revenue: 1470, trend: '-2%' },
  { id: 5, name: '龙湖天街停车场', orders: 87, revenue: 1305, trend: '+15%' }
])

const alertData = ref([
  { id: 1, title: '系统运行正常', type: 'success', description: '所有服务正常运行' },
  { id: 2, title: '2个场库离线', type: 'warning', description: '请检查网络连接' },
  { id: 3, title: '支付系统维护', type: 'info', description: '预计维护时间30分钟' }
])

// 地图和图表实例
let mapInstance = null
let orderChart = null
let revenueChart = null

// 初始化地图
const initMap = async () => {
  try {
    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: import.meta.env.VITE_AMAP_JS_SECURITY,
    }

    const AMap = await AMapLoader.load({
      key: import.meta.env.VITE_AMAP_JS_KEY,
      version: '2.0',
      plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.HeatMap', 'AMap.MarkerCluster']
    })

    mapInstance = new AMap.Map('amap-container', {
      zoom: 11,
      center: [121.81, 30.88],
      mapStyle: 'amap://styles/blue',
      showLabel: true
    })

    // 添加控件
    mapInstance.addControl(new AMap.Scale())
    mapInstance.addControl(new AMap.ToolBar())

    // 加载场库数据
    await loadWarehouseData()
    mapLoaded.value = true
    checkLoadingComplete()

  } catch (error) {
    console.error('地图初始化失败:', error)
    ElMessage.error('地图加载失败，将使用模拟数据')
    mapLoaded.value = true
    checkLoadingComplete()
  }
}

// 加载场库数据
const loadWarehouseData = async () => {
  try {
    // 模拟场库数据
    const warehouses = [
      { id: 1, name: '万达广场停车场', longitude: 121.82, latitude: 30.89 },
      { id: 2, name: '银泰城停车场', longitude: 121.80, latitude: 30.87 },
      { id: 3, name: '恒隆广场停车场', longitude: 121.83, latitude: 30.88 }
    ]
    
    const markers = warehouses.map(warehouse => {
      return new AMap.Marker({
        position: [warehouse.longitude, warehouse.latitude],
        title: warehouse.name,
        icon: new AMap.Icon({
          size: new AMap.Size(32, 32),
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiM0MDlFRkYiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTIgNEgxNFYxMkgyVjRaIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+Cjwvc3ZnPgo='
        })
      })
    })
    
    mapInstance.add(markers)
    
  } catch (error) {
    console.error('加载场库数据失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  // 订单趋势图
  orderChart = echarts.init(document.getElementById('order-chart'))
  orderChart.setOption({
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e3f2fd',
      borderWidth: 1,
      textStyle: { color: '#2c3e50' },
      axisPointer: {
        type: 'cross',
        lineStyle: { color: '#409eff', width: 1, type: 'dashed' }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: { lineStyle: { color: '#e3f2fd' } },
      axisTick: { show: false },
      axisLabel: { color: '#8492a6', fontSize: 12 }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#8492a6', fontSize: 12 },
      splitLine: { lineStyle: { color: '#f5f7fa', type: 'dashed' } }
    },
    series: [{
      data: [120, 132, 101, 134, 90, 230, 210],
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#409eff' },
          { offset: 1, color: '#66b3ff' }
        ]),
        width: 3
      },
      itemStyle: {
        color: '#409eff',
        borderColor: '#fff',
        borderWidth: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
        ])
      }
    }]
  })

  // 收入分析图
  revenueChart = echarts.init(document.getElementById('revenue-chart'))
  revenueChart.setOption({
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e3f2fd',
      borderWidth: 1,
      textStyle: { color: '#2c3e50' },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'center',
      textStyle: { color: '#8492a6', fontSize: 12 }
    },
    series: [{
      name: '收入分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['40%', '50%'],
      data: [
        {
          value: 1048,
          name: '停车费',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#409eff' },
              { offset: 1, color: '#66b3ff' }
            ])
          }
        },
        {
          value: 735,
          name: '充电费',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#67c23a' },
              { offset: 1, color: '#85d65a' }
            ])
          }
        },
        {
          value: 580,
          name: '套餐费',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#e6a23c' },
              { offset: 1, color: '#f0b659' }
            ])
          }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      labelLine: {
        show: false
      },
      label: {
        show: false
      }
    }]
  })

  chartsLoaded.value = true
  checkLoadingComplete()
}

// 检查加载完成状态
const checkLoadingComplete = () => {
  if (mapLoaded.value && chartsLoaded.value) {
    setTimeout(() => {
      loading.value = false
    }, 500) // 延迟500ms让用户看到加载完成的效果
  }
}

// 切换地图类型
const switchMapType = (type) => {
  mapType.value = type
  loadWarehouseData()
}

// 更新实时数据
const updateRealtimeData = () => {
  const events = [
    { event: '新订单', value: `¥${Math.floor(Math.random() * 50 + 10)}`, type: 'order' },
    { event: '用户注册', value: '1人', type: 'user' },
    { event: '支付完成', value: `¥${Math.floor(Math.random() * 30 + 15)}`, type: 'payment' },
    { event: '车辆入场', value: '万达广场', type: 'entry' },
    { event: '车辆出场', value: '银泰城', type: 'exit' },
    { event: '充电完成', value: `¥${Math.floor(Math.random() * 20 + 5)}`, type: 'charging' }
  ]

  const newEvent = events[Math.floor(Math.random() * events.length)]
  const now = new Date()
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

  realtimeData.value.unshift({
    id: Date.now(),
    time: timeStr,
    ...newEvent
  })

  // 保持最多6条记录
  if (realtimeData.value.length > 6) {
    realtimeData.value.pop()
  }
}

// 更新KPI数据
const updateKpiData = () => {
  kpiData.value.forEach(kpi => {
    if (kpi.key === 'orders') {
      kpi.value = Math.floor(Math.random() * 50 + 150)
    } else if (kpi.key === 'revenue') {
      kpi.value = `¥${(Math.random() * 5000 + 10000).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
    } else if (kpi.key === 'users') {
      kpi.value = Math.floor(Math.random() * 20 + 80)
    }
  })
}

let updateTimer = null

// 生命周期
onMounted(() => {
  initMap()
  initCharts()

  // 启动定时更新
  updateTimer = setInterval(() => {
    updateRealtimeData()
    updateKpiData()
  }, 5000) // 每5秒更新一次
})

onUnmounted(() => {
  if (mapInstance) mapInstance.destroy()
  if (orderChart) orderChart.dispose()
  if (revenueChart) revenueChart.dispose()
  if (updateTimer) clearInterval(updateTimer)
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-content {
  opacity: 0;
  animation: fadeIn 0.8s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.kpi-section {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
}

.main-section {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  height: 450px;
  position: relative;
  z-index: 1;
}

.map-container {
  flex: 2;
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#amap-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.chart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-item h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bottom-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  height: 350px;
  position: relative;
  z-index: 1;
}

.data-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.data-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.data-panel h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #2c3e50;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 实时数据流样式 */
.stream-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 14px;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.stream-item:hover {
  background: rgba(64, 158, 255, 0.05);
  padding-left: 8px;
  padding-right: 8px;
}

.stream-item:last-child {
  border-bottom: none;
}

.stream-icon {
  margin-right: 12px;
}

.icon-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.dot-order { background: #409eff; }
.dot-user { background: #67c23a; }
.dot-payment { background: #e6a23c; }
.dot-entry { background: #f56c6c; }
.dot-exit { background: #909399; }
.dot-charging { background: #9c27b0; }

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.stream-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time {
  color: #8492a6;
  font-size: 11px;
  font-weight: 500;
}

.event {
  color: #2c3e50;
  font-weight: 500;
}

.value {
  color: #409eff;
  font-weight: 600;
  font-size: 13px;
}

/* 排行榜样式 */
.rank-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.rank-item:hover {
  background: rgba(64, 158, 255, 0.05);
  padding-left: 8px;
  padding-right: 8px;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-right: 12px;
  color: white;
}

.rank-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; }
.rank-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%); color: #333; }
.rank-3 { background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%); }
.rank-4, .rank-5 { background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%); }

.rank-info {
  flex: 1;
}

.name {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 14px;
}

.stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.orders {
  color: #409eff;
  font-weight: 500;
}

.revenue {
  color: #67c23a;
  font-weight: 500;
}

.trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 50px;
  text-align: center;
}

.trend-up {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.trend-down {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .kpi-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .main-section {
    flex-direction: column;
    height: auto;
  }

  .map-container {
    height: 400px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    height: auto;
  }

  .data-panel {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-container {
    padding: 10px;
  }
}
</style>
