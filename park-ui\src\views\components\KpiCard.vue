<template>
  <el-card class="kpi-card" shadow="hover">
    <div class="kpi-content">
      <div class="kpi-icon">
        <el-icon :size="28" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="kpi-info">
        <div class="kpi-title">{{ title }}</div>
        <div class="kpi-value">{{ formattedValue }}</div>
        <div class="kpi-trend" :class="trendClass">
          <el-icon size="12">
            <ArrowUp v-if="trend.startsWith('+')" />
            <ArrowDown v-else />
          </el-icon>
          <span>{{ trend }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  ArrowUp, 
  ArrowDown, 
  OfficeBuilding, 
  Connection, 
  Document, 
  Money, 
  User 
} from '@element-plus/icons-vue'

const props = defineProps({
  title: String,
  value: [String, Number],
  trend: String,
  icon: String
})

const iconComponents = {
  OfficeBuilding,
  Connection,
  Document,
  Money,
  User
}

const iconComponent = computed(() => iconComponents[props.icon] || OfficeBuilding)
const iconColor = computed(() => '#409eff')

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})

const trendClass = computed(() => {
  return props.trend.startsWith('+') ? 'trend-up' : 'trend-down'
})
</script>

<style scoped>
.kpi-card {
  height: 100px;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
}

.kpi-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.kpi-icon {
  margin-right: 15px;
  padding: 10px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kpi-info {
  flex: 1;
}

.kpi-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.kpi-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 3px;
  line-height: 1.2;
}

.kpi-trend {
  display: flex;
  align-items: center;
  font-size: 11px;
  gap: 2px;
  font-weight: 500;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-up .el-icon,
.trend-down .el-icon {
  font-weight: bold;
}
</style>
